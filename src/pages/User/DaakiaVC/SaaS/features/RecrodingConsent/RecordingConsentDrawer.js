import React from "react";
import { Avatar } from "antd";
import { CheckOutlined, ExclamationOutlined, CloseOutlined } from '@ant-design/icons';
import SideDrawer from "../../../components/SideDrawer";
import { generateAvatar } from "../../../utils/helper";
import "./RecordingConsentDrawer.scss";

export function RecordingConsentDrawer({
    participantConsent,
    showRecordingConsentDrawer
}){
    const getStatusIcon = (status) => {
        if (status === 'accept') {
            return (
                <span className="icon-box accepted">
                    <CheckOutlined />
                </span>
            );
        }
        if (status === 'reject') {
            return (
                <span className="icon-box denied">
                    <CloseOutlined />
                </span>
            );
        }
        return (
            <span className="icon-box pending">
                <ExclamationOutlined />
            </span>
        );
    };

    const getStatusClass = (status) => {
        if (status === 'accept') return 'accepted';
        if (status === 'reject') return 'denied';
        return 'pending';
    };

    const getStatusText = (status) => {
        if (status === 'accept') return 'Accepted';
        if (status === 'reject') return 'Denied';
        return 'Pending';
    };

    return (
        <SideDrawer
            show={showRecordingConsentDrawer}
            title="Recording Consent"
        >
            <div className="consent-list">
                {participantConsent.map((participant) => (
                    <div key={participant.participantId} className="consent-item">
                        <div className="participant-info">
                            <Avatar className="participant-avatar">
                                {generateAvatar(participant.participantName)}
                            </Avatar>
                            <div className="participant-details">
                                <p className="participant-name">{participant.participantName}</p>
                                <p className={`consent-status ${getStatusClass(participant.consent)}`}>
                                    <span className="status-icon">
                                        {getStatusIcon(participant.consent)}
                                    </span>
                                    {getStatusText(participant.consent)}
                                </p>
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        </SideDrawer>
    )
}

