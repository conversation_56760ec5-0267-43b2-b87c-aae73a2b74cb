.consent-list {
    padding: 16px;

    .consent-item {
        margin-bottom: 16px;
        background-color: none;

        .participant-info {
            display: flex;
            align-items: center;
            gap: 12px;

            .participant-avatar {
                width: 50px;
                height: 50px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
                font-weight: 500;
                background-color: #fd4563;
            }

            .participant-details {
                flex: 1;

                .participant-name {
                    margin: 0;
                    font-size: 14px;
                    font-weight: 500;
                    color: #ffffff;
                }

                .consent-status {
                    margin: 4px 0 0;
                    font-size: 14px;
                    display: flex;
                    align-items: center;
                    gap: 6px;
                    
                    .status-icon {
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        .icon-box {
                            padding: 2px;
                            border-radius: 1px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            width: 10px;
                            height: 10px;

                            &.accepted {
                                background-color: #52c41a;
                            }

                            &.denied {
                                background-color: #ff4d4f;
                            }

                            &.pending {
                                background-color: #faad14;
                            }

                            .anticon {
                                font-size: 8px;
                                color: #000;
                                font-weight: 900;
                                svg {
                                    stroke-width: 100;
                                    stroke: currentColor;
                                }
                            }
                        }
                    }

                    &.accepted {
                        color: #52c41a;
                    }

                    &.pending {
                        color: #faad14;
                    }

                    &.denied {
                        color: #ff4d4f;
                    }
                }
            }
        }
    }
}
